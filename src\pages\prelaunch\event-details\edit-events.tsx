import { useNavigate } from 'react-router-dom';
import { useState, useRef, useEffect } from 'react';
import { Edit3, X, RefreshCw } from 'lucide-react';
import {
  ArrowLeft,
  Calendar,
  Clock,
  CloseCircle,
  Location,
} from 'iconsax-react';
import { formatDate, formattingTime } from '../../../lib/helpers';
import { useEventStore } from '../../../lib/store/event';
import { FormInput } from '../../../components/inputs/form-input/form-input';
import { AddressAutocomplete } from '../../../components/inputs/address-autocomplete';
import { DayPicker } from 'react-day-picker';
import 'react-day-picker/dist/style.css';
import { format } from 'date-fns';
import { motion, AnimatePresence } from 'framer-motion';
import { useMutation } from '@tanstack/react-query';
import { events, UpdateEventPayload } from '../../../lib/services/events';
import { useEventManagement } from '../../../lib/hooks/useEventManagement';
import { toast } from 'sonner';

export const EditEvents = () => {
  const navigate = useNavigate();
  const { selectedEvent } = useEventStore();
  const { updateEventOptimistically } = useEventManagement();

  // Modal state
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    eventName: selectedEvent?.title || '',
    eventDescription: selectedEvent?.description || '',
    eventDate: selectedEvent?.date_from || '',
    eventTime: selectedEvent?.date_from
      ? formattingTime(selectedEvent.date_from)
      : '',
    preference: 'Open Event',
    location: selectedEvent?.location_address || '',
  });

  // Date picker state
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({
    from: selectedEvent?.date_from
      ? new Date(selectedEvent.date_from)
      : undefined,
    to: selectedEvent?.date_to ? new Date(selectedEvent.date_to) : undefined,
  });

  // Time picker state
  const [showTimePicker, setShowTimePicker] = useState(false);
  const timePickerRef = useRef<HTMLDivElement>(null);

  const [images, setImages] = useState([
    {
      id: 1,
      url: 'https://images.unsplash.com/photo-1464047736614-af63643285bf?w=800&h=600&fit=crop',
      alt: 'Event image 1',
    },
    {
      id: 2,
      url: 'https://images.unsplash.com/photo-1511795409834-ef04bbd61622?w=800&h=600&fit=crop',
      alt: 'Event image 2',
    },
    {
      id: 3,
      url: 'https://images.unsplash.com/photo-1530103862676-de8c9debad1d?w=800&h=600&fit=crop',
      alt: 'Event image 3',
    },
  ]);

  const [hoveredImage, setHoveredImage] = useState<number | null>(null);

  // Time options for dropdown
  const timeOptions = Array.from({ length: 48 }, (_, i) => {
    const hour = Math.floor(i / 2);
    const minute = i % 2 === 0 ? '00' : '30';
    return `${hour.toString().padStart(2, '0')}:${minute}`;
  });

  // Update event mutation
  const updateEventMutation = useMutation({
    mutationFn: (payload: UpdateEventPayload) => {
      if (!selectedEvent?.id) throw new Error('No event selected');
      return events.updateEventDetails(selectedEvent.id, payload);
    },
    onSuccess: (response) => {
      const updatedEvent = response.data;
      updateEventOptimistically(updatedEvent);
      setIsEditModalOpen(false);
      toast.success('Event updated successfully!');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to update event');
    },
  });

  // Handle form input changes
  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  // Handle date range selection
  const handleDateRangeSelect = (
    range: { from?: Date; to?: Date } | undefined
  ) => {
    if (range) {
      setDateRange(range);
      if (range.from) {
        const dateStr = format(range.from, 'yyyy-MM-dd');
        handleInputChange('eventDate', dateStr);
      }
    }
  };

  // Handle time selection
  const handleTimeSelect = (time: string) => {
    handleInputChange('eventTime', time);
    setShowTimePicker(false);
  };

  // Handle form submission
  const handleSaveChanges = () => {
    if (!selectedEvent?.id) return;

    const payload: UpdateEventPayload = {
      title: formData.eventName,
      description: formData.eventDescription,
      date_from: dateRange.from
        ? format(dateRange.from, "yyyy-MM-dd'T'HH:mm:ss")
        : undefined,
      date_to: dateRange.to
        ? format(dateRange.to, "yyyy-MM-dd'T'HH:mm:ss")
        : undefined,
    };

    updateEventMutation.mutate(payload);
  };

  // Handle click outside for time picker
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        timePickerRef.current &&
        !timePickerRef.current.contains(event.target as Node)
      ) {
        setShowTimePicker(false);
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        if (showDatePicker) {
          setShowDatePicker(false);
        } else if (showTimePicker) {
          setShowTimePicker(false);
        } else if (isEditModalOpen) {
          setIsEditModalOpen(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [showDatePicker, showTimePicker, isEditModalOpen]);

  const changeImage = (imageId: number) => {
    const newImageUrl = `https://images.unsplash.com/photo-${Math.floor(
      Math.random() * 1000000000
    )}?w=800&h=600&fit=crop`;
    setImages(
      images.map((img) =>
        img.id === imageId ? { ...img, url: newImageUrl } : img
      )
    );
  };

  const deleteImage = (imageId: number) => {
    setImages(images.filter((img) => img.id !== imageId));
  };

  const eventDate = formatDate(selectedEvent?.date_from);
  const eventTime = formattingTime(selectedEvent?.date_from);

  // Format date range text for display
  const dateRangeText =
    dateRange.from && dateRange.to
      ? `${format(dateRange.from, 'MMM dd')} - ${format(
          dateRange.to,
          'MMM dd, yyyy'
        )}`
      : dateRange.from
      ? format(dateRange.from, 'MMM dd, yyyy')
      : 'Select Date Range';
  return (
    <div className="bg-[linear-gradient(229.47deg,_#FEFAF8_38.81%,_#F5F6FE_851.11%)]">
      <div className=" min-h-screen max-w-[1160px] mx-auto">
        {/* Header */}
        <div className="flex items-center gap-3 py-8">
          <button
            type="button"
            onClick={() => navigate(-1)}
            className={` bg-white rounded-full cursor-pointer `}>
            <div className="bg-[#F5F6FE] w-fit p-0.5">
              <div className="bg-primary p-0.5 rounded-full">
                <ArrowLeft size="16" color="#fff" />
              </div>{' '}
            </div>
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Event Details</h1>
        </div>

        {/* Content */}
        <div className="">
          {/* Event Header */}
          <div className="flex items-center space-x-4 mb-8 bg-white py-9 px-10">
            <div className="w-[130px] h-[130px] rounded-xl overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1530103862676-de8c9debad1d?w=800&h=600&fit=crop"
                alt="Profile"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <h2 className="text-[36px] font-medium">Tolani's Birthday</h2>
                <button
                  onClick={() => setIsEditModalOpen(true)}
                  className="text-dark-blue-100 text-sm bg-primary-250 px-3.5 py-2 rounded-full font-semibold">
                  Edit Details{' '}
                </button>
              </div>
              <p className="text-grey-250 text-base">
                My 18th birthday celebration
              </p>
              <div className=" flex gap-4 my-4">
                <div className="flex items-center gap-1 bg-cus-pink-500 pl-2.5 pr-2 py-0.5 rounded-2xl">
                  <Calendar color="#FF885E" size={12} variant="Bulk" />{' '}
                  <span className="text-xs italic font-medium text-cus-orange-250">
                    {eventDate}
                  </span>
                </div>
                <div className="flex items-center gap-1 bg-primary-700 pl-2.5 pr-2 rounded-2xl py-0.5">
                  <Clock color="#000073" size={12} variant="Bulk" />{' '}
                  <span className="text-dark-blue text-xs italic font-medium">
                    {eventTime}
                  </span>
                </div>
              </div>
              <div className=" mt-2 uppercase text-grey-950 text-xs italic">
                📌{' '}
                <span>
                  Eko Hotels, 1415 Adetokunbo Ademola Street, Victoria Island,
                  Lagos 106104, Lagos
                </span>
              </div>
            </div>
          </div>

          {/* Image Gallery */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            {/* Large Image */}
            <div
              className="relative group cursor-pointer rounded-xl overflow-hidden"
              onMouseEnter={() => setHoveredImage(images[0]?.id)}
              onMouseLeave={() => setHoveredImage(null)}>
              {images[0] && (
                <>
                  <img
                    src={images[0].url}
                    alt={images[0].alt}
                    className="w-[567px] h-[460px] object-cover"
                  />
                  {hoveredImage === images[0].id && (
                    <div className="absolute inset-0 bg-black/50 flex items-center justify-center space-x-3">
                      <button
                        onClick={() => changeImage(images[0].id)}
                        className="px-4 py-2 bg-white text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-100 flex items-center space-x-2">
                        <RefreshCw size={14} />
                        <span>Change Image</span>
                      </button>
                      <button
                        onClick={() => deleteImage(images[0].id)}
                        className="px-4 py-2 bg-red-600 text-white rounded-lg text-sm font-medium hover:bg-red-700 flex items-center space-x-2">
                        <X size={14} />
                        <span>Delete Image</span>
                      </button>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Edit Event Modal */}
      <AnimatePresence>
        {isEditModalOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 backdrop-blur-sm"
            onClick={() => setIsEditModalOpen(false)}>
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              transition={{ duration: 0.2 }}
              className="mx-4 max-h-[90vh] w-full max-w-[600px] relative overflow-y-auto rounded-2xl bg-white shadow-xl sm:mx-0"
              onClick={(e) => e.stopPropagation()}>
              {/* Modal Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-100">
                <h3 className="text-xl font-semibold text-gray-900">
                  Edit Event Details
                </h3>
                <button
                  onClick={() => setIsEditModalOpen(false)}
                  className="p-1 hover:bg-gray-100 rounded-full transition-colors">
                  <X size={20} className="text-gray-500" />
                </button>
              </div>

              {/* Modal Content */}
              <div className="p-6 space-y-6">
                {/* Event Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Event Name
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      value={formData.eventName}
                      onChange={(e) =>
                        handleInputChange('eventName', e.target.value)
                      }
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      placeholder="Enter event name"
                    />
                    <Edit3
                      size={16}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-primary"
                    />
                  </div>
                </div>

                {/* Event Description */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Event Description
                  </label>
                  <div className="relative">
                    <textarea
                      value={formData.eventDescription}
                      onChange={(e) =>
                        handleInputChange('eventDescription', e.target.value)
                      }
                      rows={3}
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
                      placeholder="Enter event description"
                    />
                    <Edit3
                      size={16}
                      className="absolute right-3 top-3 text-primary"
                    />
                  </div>
                </div>

                {/* Event Date and Time */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Event Date */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Event Date
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        value={dateRangeText}
                        readOnly
                        onClick={() => setShowDatePicker(true)}
                        className="w-full px-4 py-3 pl-12 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent cursor-pointer"
                        placeholder="Select date"
                      />
                      <Calendar
                        size={20}
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary cursor-pointer"
                        onClick={() => setShowDatePicker(true)}
                      />
                    </div>
                  </div>

                  {/* Time of Event */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Time of Event
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        value={formData.eventTime}
                        readOnly
                        onClick={() => setShowTimePicker(true)}
                        className="w-full px-4 py-3 pl-12 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent cursor-pointer"
                        placeholder="Select time"
                      />
                      <Clock
                        size={20}
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary cursor-pointer"
                        onClick={() => setShowTimePicker(true)}
                      />

                      {/* Time Picker Dropdown */}
                      {showTimePicker && (
                        <div
                          ref={timePickerRef}
                          className="absolute z-10 mt-2 bg-white rounded-lg shadow-lg border border-gray-200 left-0 w-full max-h-[200px] overflow-y-auto">
                          <div className="py-2">
                            {timeOptions.map((time) => (
                              <button
                                key={time}
                                onClick={() => handleTimeSelect(time)}
                                className={`w-full text-left px-4 py-2 hover:bg-primary-50 text-sm ${
                                  formData.eventTime === time
                                    ? 'bg-primary-50 text-primary font-medium'
                                    : 'text-gray-700'
                                }`}>
                                {time}
                              </button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Preference */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Preference
                  </label>
                  <div className="relative">
                    <select
                      value={formData.preference}
                      onChange={(e) =>
                        handleInputChange('preference', e.target.value)
                      }
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent appearance-none bg-white">
                      <option value="Open Event">Open Event</option>
                      <option value="Private Event">Private Event</option>
                      <option value="Invite Only">Invite Only</option>
                    </select>
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                      <svg
                        className="w-5 h-5 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Location */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Location
                  </label>
                  <div className="relative">
                    <AddressAutocomplete
                      value={formData.location}
                      onChange={(value) => handleInputChange('location', value)}
                      placeholder="Enter event location"
                      className="w-full px-4 py-3 pl-12 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                    <Location
                      size={20}
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary pointer-events-none"
                    />
                    <Edit3
                      size={16}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-primary"
                    />
                  </div>
                </div>
              </div>

              {/* Modal Footer */}
              <div className="p-6 border-t border-gray-100">
                <button
                  onClick={handleSaveChanges}
                  disabled={updateEventMutation.isPending}
                  className="w-full bg-primary text-white py-3 px-6 rounded-full font-semibold hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                  {updateEventMutation.isPending
                    ? 'Saving Changes...'
                    : 'Save Changes'}
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Date Picker Modal */}
      <AnimatePresence>
        {showDatePicker && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 backdrop-blur-sm"
            onClick={() => setShowDatePicker(false)}>
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              transition={{ duration: 0.2 }}
              className="mx-4 max-h-[90vh] w-full max-w-[522px] relative overflow-y-auto rounded-2xl bg-white shadow-xl sm:mx-0"
              onClick={(e) => e.stopPropagation()}>
              <div className="text-center flex-1 pr-6 mt-8">
                <h3 className="text-[28px] font-medium text-dark-200">
                  Event Date
                </h3>
                <p className="mt-1 text-base text-grey-250">
                  Please choose the date(s) for your event
                </p>
              </div>
              <button
                onClick={() => setShowDatePicker(false)}
                className="absolute top-4 right-4 transition-colors">
                <CloseCircle size="33" color="#4D55F2" variant="Bulk" />
              </button>
              <div className="p-6 flex justify-center">
                <style>
                  {`
                    .rdp-caption_label {
                      color: #000;
                      font-weight: 600;
                      text-align: center;
                      font-size: 16px;
                      font-style: italic;
                    }
                    .rdp-day_range_middle {
                      background-color: rgba(77, 85, 242, 0.1);
                    }
                    .rdp {
                      margin: 0;
                      display: flex;
                      justify-content: center;
                    }
                    .rdp-months {
                      display: flex;
                      justify-content: center;
                    }
                    .rdp-month {
                      margin: 0;
                    }
                    .rdp-table {
                      margin: 0 auto;
                    }
                    .rdp-caption {
                      display: flex;
                      justify-content: center;
                      align-items: center;
                      margin-bottom: 1rem;
                    }
                    .rdp-nav {
                      display: flex;
                      align-items: center;
                    }
                  `}
                </style>
                <DayPicker
                  mode="range"
                  selected={dateRange}
                  onSelect={handleDateRangeSelect}
                  disabled={{ before: new Date() }}
                  startMonth={new Date()}
                  className="w-full flex justify-center"
                />
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
