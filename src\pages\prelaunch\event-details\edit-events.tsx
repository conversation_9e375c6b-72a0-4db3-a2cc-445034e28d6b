import { useNavigate } from 'react-router-dom';
import { useState } from 'react';
import {  Edit3, X, RefreshCw } from 'lucide-react';
import { ArrowLeft ,Calendar, Clock,} from 'iconsax-react';
import { formatDate, formattingTime } from '../../../lib/helpers';
import { useEventStore } from '../../../lib/store/event';

export const EditEvents = () => {
  const navigate = useNavigate();
  const { selectedEvent } = useEventStore();
  const [images, setImages] = useState([
    {
      id: 1,
      url: 'https://images.unsplash.com/photo-1464047736614-af63643285bf?w=800&h=600&fit=crop',
      alt: 'Event image 1',
    },
    {
      id: 2,
      url: 'https://images.unsplash.com/photo-1511795409834-ef04bbd61622?w=800&h=600&fit=crop',
      alt: 'Event image 2',
    },
    {
      id: 3,
      url: 'https://images.unsplash.com/photo-1530103862676-de8c9debad1d?w=800&h=600&fit=crop',
      alt: 'Event image 3',
    },
  ]);

  const [hoveredImage, setHoveredImage] = useState(null);

  const changeImage = (imageId) => {
    const newImageUrl = `https://images.unsplash.com/photo-${Math.floor(
      Math.random() * 1000000000
    )}?w=800&h=600&fit=crop`;
    setImages(
      images.map((img) =>
        img.id === imageId ? { ...img, url: newImageUrl } : img
      )
    );
  };

  const deleteImage = (imageId) => {
    setImages(images.filter((img) => img.id !== imageId));
  };
  const eventDate = formatDate(selectedEvent?.date_from);
  const eventTime = formattingTime(selectedEvent?.date_from);
  return (
    <div className="bg-[linear-gradient(229.47deg,_#FEFAF8_38.81%,_#F5F6FE_851.11%)]">
      <div className=" min-h-screen max-w-[1160px] mx-auto">
        {/* Header */}
        <div className="flex items-center gap-3 py-8">
          <button
            type="button"
            onClick={() => navigate(-1)}
            className={` bg-white rounded-full cursor-pointer `}>
            <div className="bg-[#F5F6FE] w-fit p-0.5">
              <div className="bg-primary p-0.5 rounded-full">
                <ArrowLeft size="16" color="#fff" />
              </div>{' '}
            </div>
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Event Details</h1>
        </div>

        {/* Content */}
        <div className="">
          {/* Event Header */}
          <div className="flex items-center space-x-4 mb-8 bg-white py-9 px-10">
            <div className="w-[130px] h-[130px] rounded-xl overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1530103862676-de8c9debad1d?w=800&h=600&fit=crop"
                alt="Profile"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <h2 className="text-[36px] font-medium">Tolani's Birthday</h2>
                <button className="text-dark-blue-100 text-sm bg-[#EDEEFE]">
                  Edit Details{' '}
                </button>
              </div>
              <p className="text-grey-250 text-base">
                My 18th birthday celebration
              </p>
              <div className=" flex gap-4 my-4">
                <div className="flex items-center gap-1 bg-cus-pink-500 pl-2.5 pr-2 py-0.5 rounded-2xl">
                  <Calendar color="#FF885E" size={12} variant="Bulk" />{' '}
                  <span className="text-xs italic font-medium text-cus-orange-250">
                    {eventDate}
                  </span>
                </div>
                <div className="flex items-center gap-1 bg-primary-700 pl-2.5 pr-2 rounded-2xl py-0.5">
                  <Clock color="#000073" size={12} variant="Bulk" />{' '}
                  <span className="text-dark-blue text-xs italic font-medium">
                    {eventTime}
                  </span>
                </div>
              </div>
              <div className=" mt-2 uppercase text-grey-950 text-xs italic">
                📌{' '}
                <span>
                  Eko Hotels, 1415 Adetokunbo Ademola Street, Victoria Island,
                  Lagos 106104, Lagos
                </span>
              </div>
            </div>
          </div>

          {/* Image Gallery */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            {/* Large Image */}
            <div
              className="relative group cursor-pointer rounded-xl overflow-hidden"
              onMouseEnter={() => setHoveredImage(images[0]?.id)}
              onMouseLeave={() => setHoveredImage(null)}>
              {images[0] && (
                <>
                  <img
                    src={images[0].url}
                    alt={images[0].alt}
                    className="w-[567px] h-[460px] object-cover"
                  />
                  {hoveredImage === images[0].id && (
                    <div className="absolute inset-0 bg-black/50 flex items-center justify-center space-x-3">
                      <button
                        onClick={() => changeImage(images[0].id)}
                        className="px-4 py-2 bg-white text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-100 flex items-center space-x-2">
                        <RefreshCw size={14} />
                        <span>Change Image</span>
                      </button>
                      <button
                        onClick={() => deleteImage(images[0].id)}
                        className="px-4 py-2 bg-red-600 text-white rounded-lg text-sm font-medium hover:bg-red-700 flex items-center space-x-2">
                        <X size={14} />
                        <span>Delete Image</span>
                      </button>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
